@extends('seller.layouts.app')

@push('scripts')
    <script>
        // Pass the category mappings from the server to JavaScript
        const legacyCategoryMapping = @json($legacyCategoryMapping ?? []);
        const legacySubcategoryMapping = @json($legacySubcategoryMapping ?? []);
    </script>
    <script src="{{ asset(js_path() . '/category-selector.js') }}"></script>
    <script src="{{ asset('dev-js/course-resources.js') }}"></script>
    <script src="{{ asset('dev-js/product.js') }}"></script>
@endpush

@section('content')
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <div class="space-y-6">
        <div class="flex items-center gap-4">
            <a href="{{ route('seller.products.index', [], false) ?? '#' }}"
                class="rounded-lg p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                    <path d="m15 18-6-6 6-6"></path>
                </svg>
                <span class="sr-only">Back</span>
            </a>
            <div>
                <h1 class="text-3xl font-bold tracking-tight text-gray-900">Edit
                    @if(isset($productType))
                        @switch($productType)
                            @case('ebook')
                                Ebook
                                @break
                            @case('digital')
                                Digital Product
                                @break
                            @case('course')
                                Course/Tutorial
                                @break
                            @default
                                Product
                        @endswitch
                    @else
                        Product
                    @endif
                </h1>
                <p class="text-gray-600">
                    @if(isset($productType))
                        @switch($productType)
                            @case('ebook')
                                Update your ebook details and content
                                @break
                            @case('digital')
                                Update your digital product details and files
                                @break
                            @case('course')
                                Update your course content and educational materials
                                @break
                            @default
                                Update your digital product details
                        @endswitch
                    @else
                        Update your digital product details
                    @endif
                </p>
            </div>
        </div>

        <!-- Product Type Indicator -->
        @if(isset($productType))
            <div class="rounded-xl border bg-white shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="rounded-full p-2
                            @switch($productType)
                                @case('ebook') bg-blue-100 @break
                                @case('digital') bg-green-100 @break
                                @case('course') bg-purple-100 @break
                            @endswitch">
                            <svg class="h-5 w-5
                                @switch($productType)
                                    @case('ebook') text-blue-600 @break
                                    @case('digital') text-green-600 @break
                                    @case('course') text-purple-600 @break
                                @endswitch" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                @switch($productType)
                                    @case('ebook')
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                        @break
                                    @case('digital')
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                                        @break
                                    @case('course')
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                        @break
                                @endswitch
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-900">Product Type:
                                @switch($productType)
                                    @case('ebook') Ebook @break
                                    @case('digital') Digital Product @break
                                    @case('course') Course/Tutorial @break
                                @endswitch
                            </h3>
                            <p class="text-xs text-gray-500">
                                @switch($productType)
                                    @case('ebook') Digital books and written content @break
                                    @case('digital') Software, templates, and digital tools @break
                                    @case('course') Structured learning with lessons @break
                                @endswitch
                            </p>
                        </div>
                    </div>
                    <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">Editing</span>
                </div>
            </div>
        @endif

        <form id="product-form" action="{{ route('seller.products.update', $product, false) ?? '#' }}" method="POST"
            enctype="multipart/form-data" onsubmit="if(typeof tinyMCE !== 'undefined') { tinyMCE.triggerSave(); }" autocomplete="off">
            @csrf
            @method('PUT')

            <!-- Hidden fields for product type -->
            @if(isset($productType))
                <input type="hidden" name="product_type" value="{{ $productType }}">
            @endif
            @if(isset($contentType))
                <input type="hidden" name="content_type" value="{{ $contentType }}">
            @endif
            <div class="grid gap-6 md:grid-cols-6">
                <div class="space-y-6 md:col-span-4">
                    <div class="rounded-xl border bg-white shadow-lg">
                        <div class="border-b border-gray-100 p-6">
                            <h3 class="text-lg font-semibold text-gray-900">Product Information</h3>
                            <p class="text-sm text-gray-600">Basic information about your digital product</p>
                        </div>
                        <div class="p-6 space-y-5">
                            <div class="space-y-2">
                                <label for="name" class="block text-sm font-medium text-gray-700">
                                    Product Name <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="name" id="name"
                                    class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                    placeholder="e.g. Financial Planning Spreadsheet"
                                    value="{{ old('name', $product->name) }}" required>
                                @error('name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            <div class="space-y-2">
                                <label for="description" class="block text-sm font-medium text-gray-700">
                                    Description <span class="text-red-500">*</span>
                                </label>
                                <textarea name="description" id="description" rows="6"
                                    class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                    placeholder="Describe your product in detail..." required>{{ old('description', $product->description) }}</textarea>
                                <p class="text-xs text-gray-500">
                                    Provide a detailed description of your product, including its features and benefits.
                                </p>
                                @error('description')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            <div class="space-y-2">
                                <label for="category" class="block text-sm font-medium text-gray-700">
                                    Legacy Category <span class="text-red-500">*</span>
                                </label>
                                <select name="category" id="category"
                                    class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                    required>
                                    <option value="">Select a category</option>
                                    @foreach ($categoryGroups ?? [] as $groupName => $categories)
                                        <optgroup label="{{ $groupName }}">
                                            @foreach ($categories as $value => $label)
                                                <option value="{{ $value }}"
                                                    {{ old('category', $product->category) == $value ? 'selected' : '' }}>
                                                    {{ $label }}</option>
                                            @endforeach
                                        </optgroup>
                                    @endforeach
                                </select>
                                @error('category')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="space-y-2">
                                <label for="category_search" class="block text-sm font-medium text-gray-700">
                                    Detailed Category <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <input type="text" id="category_search"
                                        class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                        placeholder="Search for a category..." autocomplete="off">
                                    <div id="category_dropdown"
                                        class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-200 hidden max-h-60 overflow-y-auto">
                                        <div class="p-2">
                                            @foreach ($categoryTree ?? [] as $topCategory)
                                                <div class="category-group mb-2">
                                                    <div class="font-medium text-gray-700 py-1">{{ $topCategory->name }}
                                                    </div>
                                                    @foreach ($topCategory->activeSubcategories as $subCategory)
                                                        <div class="pl-3 mb-1">
                                                            <div class="font-medium text-gray-600 py-1">
                                                                {{ $subCategory->name }}</div>
                                                            @foreach ($subCategory->activeDetailedCategories as $detailedCategory)
                                                                <div class="pl-3">
                                                                    <div class="category-option py-1 px-2 hover:bg-gray-100 rounded cursor-pointer"
                                                                        data-id="{{ $detailedCategory->id }}"
                                                                        data-category-id="{{ $topCategory->id }}"
                                                                        data-subcategory-id="{{ $subCategory->id }}"
                                                                        data-name="{{ $topCategory->name }} > {{ $subCategory->name }} > {{ $detailedCategory->name }}">
                                                                        {{ $detailedCategory->name }}
                                                                    </div>
                                                                </div>
                                                            @endforeach
                                                        </div>
                                                    @endforeach
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                                <input type="hidden" name="category_id" id="category_id"
                                    value="{{ old('category_id', $product->category_id) }}">
                                <input type="hidden" name="subcategory_id" id="subcategory_id"
                                    value="{{ old('subcategory_id', $product->subcategory_id) }}">
                                <input type="hidden" name="detailed_category_id" id="detailed_category_id"
                                    value="{{ old('detailed_category_id', $product->detailed_category_id) }}">
                                <div id="selected_category"
                                    class="mt-2 p-2 bg-gray-50 rounded-md {{ old('detailed_category_id', $product->detailed_category_id) ? '' : 'hidden' }}">
                                    <div class="flex items-center justify-between">
                                        <span id="selected_category_name" class="text-sm text-gray-700">
                                            @if ($product->productDetailedCategory)
                                                {{ $product->categoryPath }}
                                            @endif
                                        </span>
                                        <button type="button" id="clear_category"
                                            class="text-sm text-red-500 hover:text-red-700">
                                            Clear
                                        </button>
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500">
                                    Select the most specific category for your product to help buyers find it.
                                </p>
                                @error('category_id')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="rounded-xl border bg-white shadow-lg">
                        <div class="border-b border-gray-100 p-6">
                            <h3 class="text-lg font-semibold text-gray-900">Product Images <span
                                    class="text-red-500">*</span></h3>
                            <p class="text-sm text-gray-600">Update the images for your product (required when publishing).
                                The first image will be used as the cover.</p>
                        </div>
                        <div class="p-6 space-y-5">
                            @if ($product->images->count() > 0)
                                <div class="space-y-2">
                                    <div class="flex justify-between items-center">
                                        <label class="block text-sm font-medium text-gray-700">Current Images</label>
                                        <p class="text-xs text-gray-500">Drag to reorder. First image is the cover.</p>
                                    </div>
                                    <div id="sortable-images" class="space-y-2">
                                        @foreach ($product->images as $image)
                                            <div class="flex items-center justify-between rounded-lg border border-gray-200 p-3 existing-image-item"
                                                data-image-id="{{ $image->id }}"
                                                data-sort-order="{{ $image->sort_order }}">
                                                <div class="flex items-center gap-3">
                                                    <div class="cursor-move handle">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16"
                                                            height="16" viewBox="0 0 24 24" fill="none"
                                                            stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                            stroke-linejoin="round" class="h-5 w-5 text-gray-400">
                                                            <line x1="21" y1="10" x2="3"
                                                                y2="10"></line>
                                                            <line x1="21" y1="6" x2="3"
                                                                y2="6"></line>
                                                            <line x1="21" y1="14" x2="3"
                                                                y2="14"></line>
                                                            <line x1="21" y1="18" x2="3"
                                                                y2="18"></line>
                                                        </svg>
                                                    </div>
                                                    <img src="{{ asset('storage/' . $image->path) }}"
                                                        alt="{{ $product->name }}"
                                                        class="h-12 w-12 rounded-lg object-cover shadow-sm">
                                                    <div>
                                                        <p class="text-sm font-medium text-gray-900 image-label">
                                                            {{ $image->is_primary ? '📌 Cover Image' : 'Product Image' }}
                                                        </p>
                                                        <p class="text-xs text-gray-500">Uploaded previously</p>
                                                    </div>
                                                </div>
                                                <div class="flex items-center gap-2">
                                                    <button type="button"
                                                        class="set-primary-image rounded-lg p-2 text-gray-500 hover:bg-gray-100 hover:text-indigo-600 transition-colors {{ $image->is_primary ? 'hidden' : '' }}"
                                                        data-image-id="{{ $image->id }}">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16"
                                                            height="16" viewBox="0 0 24 24" fill="none"
                                                            stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                            stroke-linejoin="round" class="h-5 w-5">
                                                            <path
                                                                d="M12 2L15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2z">
                                                            </path>
                                                        </svg>
                                                    </button>
                                                    <button type="button"
                                                        class="remove-existing-image rounded-lg p-2 text-gray-500 hover:bg-gray-100 hover:text-red-600 transition-colors"
                                                        data-image-id="{{ $image->id }}">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16"
                                                            height="16" viewBox="0 0 24 24" fill="none"
                                                            stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                            stroke-linejoin="round" class="h-5 w-5">
                                                            <path d="M3 6h18"></path>
                                                            <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                                            <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                                                            <line x1="10" y1="11" x2="10"
                                                                y2="17"></line>
                                                            <line x1="14" y1="11" x2="14"
                                                                y2="17"></line>
                                                        </svg>
                                                    </button>
                                                </div>
                                                <input type="hidden" name="image_order[]" value="{{ $image->id }}">
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @elseif($product->image)
                                <div class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">Current Image</label>
                                    <div
                                        class="flex items-center justify-between rounded-lg border border-gray-200 p-3 existing-image">
                                        <div class="flex items-center gap-3">
                                            <img src="{{ asset('storage/' . $product->image) }}"
                                                alt="{{ $product->name }}"
                                                class="h-12 w-12 rounded-lg object-cover shadow-sm">
                                            <div>
                                                <p class="text-sm font-medium text-gray-900">Current Image</p>
                                                <p class="text-xs text-gray-500">Uploaded previously</p>
                                            </div>
                                        </div>
                                        <button type="button"
                                            class="remove-image rounded-lg p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600 transition-colors">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                class="h-5 w-5">
                                                <path d="M3 6h18"></path>
                                                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                                                <line x1="10" y1="11" x2="10" y2="17">
                                                </line>
                                                <line x1="14" y1="11" x2="14" y2="17">
                                                </line>
                                            </svg>
                                        </button>
                                        <input type="hidden" name="remove_image" class="remove-image-input"
                                            value="">
                                    </div>
                                </div>
                            @endif

                            <div class="rounded-lg border-2 border-dashed border-gray-200 p-8 upload-area">
                                <div class="flex flex-col items-center justify-center gap-3 text-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round" class="h-10 w-10 text-gray-400">
                                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                        <polyline points="17 8 12 3 7 8"></polyline>
                                        <line x1="12" y1="3" x2="12" y2="15"></line>
                                    </svg>
                                    <p class="text-sm font-medium text-gray-700">Drag and drop images here or click to
                                        browse</p>
                                    <p class="text-xs text-gray-500">
                                        Upload product images (JPEG, PNG, JPG, GIF, max 2MB each, up to 10 images)
                                    </p>
                                    <input id="images" name="images[]" type="file" class="hidden"
                                        accept="image/jpeg,image/png,image/jpg,image/gif" multiple>
                                    <label for="images"
                                        class="choose-images mt-2 inline-flex items-center rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 transition-colors cursor-pointer">
                                        Choose Images
                                    </button>
                                </div>
                            </div>
                            <div id="images-preview" class="space-y-2 hidden">
                                <label class="block text-sm font-medium text-gray-700">Newly Uploaded Images</label>
                                <div id="images-list" class="space-y-2"></div>
                            </div>
                            @error('images')
                                <div class="mt-3 p-3 rounded-lg bg-red-50 border border-red-200">
                                    <div class="flex items-center gap-2 text-red-600">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <line x1="12" y1="8" x2="12" y2="12"></line>
                                            <line x1="12" y1="16" x2="12.01" y2="16"></line>
                                        </svg>
                                        <span class="text-sm font-medium">{{ $message }}</span>
                                    </div>
                                </div>
                            @enderror
                            @error('images.*')
                                <div class="mt-3 p-3 rounded-lg bg-red-50 border border-red-200">
                                    <div class="flex items-center gap-2 text-red-600">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <line x1="12" y1="8" x2="12" y2="12"></line>
                                            <line x1="12" y1="16" x2="12.01" y2="16"></line>
                                        </svg>
                                        <span class="text-sm font-medium">{{ $message }}</span>
                                    </div>
                                </div>
                            @enderror
                        </div>
                    </div>

                    <!-- Hidden input for content type - always simple for digital products -->
                    <input type="hidden" name="content_type" value="simple">

                    <!-- Product Files Section -->
                    <div class="mt-6">
                                    <div class="rounded-xl border bg-white shadow-lg">
                                        <div class="border-b border-gray-100 p-6">
                                            <h3 class="text-lg font-semibold text-gray-900">Product Files <span
                                                    class="text-red-500">*</span></h3>
                                            <p class="text-sm text-gray-600">Manage the files that customers will receive (required when
                                                publishing)</p>
                                        </div>
                        <div class="p-6 space-y-5">
                            @php
                                $existingFiles = [];
                                if ($product->files) {
                                    $decoded = json_decode($product->files, true);
                                    if (is_array($decoded)) {
                                        // For course products, files field may contain session data instead of actual files
                                        if ($product->content_type === 'course') {
                                            // Skip course session data, only show actual files
                                            if (!isset($decoded['course_session_data'])) {
                                                $existingFiles = $decoded;
                                            }
                                        } else {
                                            $existingFiles = $decoded;
                                        }
                                    }
                                }
                            @endphp
                            @if (!empty($existingFiles))
                                <div class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">Existing Files</label>
                                    <div id="existing-file-list" class="space-y-2">
                                        @foreach ($existingFiles as $index => $file)
                                            @if(is_array($file) && isset($file['name']) && isset($file['size']))
                                            <div class="flex items-center justify-between rounded-lg border border-gray-200 p-3 existing-file-item"
                                                data-index="{{ $index }}">
                                                <div class="flex items-center gap-3">
                                                    <div
                                                        class="flex h-12 w-12 items-center justify-center rounded-lg bg-gray-100">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="16"
                                                            height="16" viewBox="0 0 24 24" fill="none"
                                                            stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                            stroke-linejoin="round" class="h-6 w-6 text-gray-500">
                                                            <path
                                                                d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z">
                                                            </path>
                                                            <polyline points="14 2 14 8 20 8"></polyline>
                                                            <line x1="16" y1="13" x2="8"
                                                                y2="13"></line>
                                                            <line x1="16" y1="17" x2="8"
                                                                y2="17"></line>
                                                            <line x1="10" y1="9" x2="8"
                                                                y2="9"></line>
                                                        </svg>
                                                    </div>
                                                    <div>
                                                        <p class="text-sm font-medium text-gray-900">{{ $file['name'] }}
                                                        </p>
                                                        <p class="text-xs text-gray-500">
                                                            {{ number_format(($file['size'] ?? 0) / 1024 / 1024, 2) }} MB</p>
                                                    </div>
                                                </div>
                                                <button type="button"
                                                    class="remove-existing-file rounded-lg p-2 text-gray-500 hover:bg-gray-100 hover:text-gray-600 transition-colors"
                                                    data-index="{{ $index }}">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="h-5 w-5">
                                                        <path d="M3 6h18"></path>
                                                        <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                                        <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                                                        <line x1="10" y1="11" x2="10"
                                                            y2="17"></line>
                                                        <line x1="14" y1="11" x2="14"
                                                            y2="17"></line>
                                                    </svg>
                                                </button>
                                                <input type="hidden" name="remove_files[]" class="remove-file-input"
                                                    value="" data-index="{{ $index }}">
                                            </div>
                                            @endif
                                        @endforeach
                                    </div>
                                </div>
                            @endif

                            <div class="rounded-lg border-2 border-dashed border-gray-200 p-8 upload-area">
                                <div class="flex flex-col items-center justify-center gap-3 text-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round" class="h-10 w-10 text-gray-400">
                                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                        <polyline points="17 8 12 3 7 8"></polyline>
                                        <line x1="12" y1="3" x2="12" y2="15"></line>
                                    </svg>
                                    <p class="text-sm font-medium text-gray-700">Drag and drop files here or click to
                                        browse</p>
                                    <p class="text-xs text-gray-500">
                                        Upload additional files that customers will download after purchase (ZIP, PDF, DOCX,
                                        XLSX, max 20MB each, up to 5 files)
                                    </p>
                                    <input id="files" name="files[]" type="file" class="hidden" multiple
                                        accept=".zip,.pdf,.docx,.xlsx">
                                    <label for="files"
                                        class="choose-files mt-2 inline-flex items-center rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 transition-colors cursor-pointer">
                                        Choose Files
                                    </button>
                                </div>
                            </div>
                            <div id="file-preview" class="space-y-2 hidden">
                                <label class="block text-sm font-medium text-gray-700">Newly Uploaded Files</label>
                                <div id="file-list" class="space-y-2"></div>
                            </div>
                            @error('files')
                                <div class="mt-3 p-3 rounded-lg bg-red-50 border border-red-200">
                                    <div class="flex items-center gap-2 text-red-600">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <line x1="12" y1="8" x2="12" y2="12"></line>
                                            <line x1="12" y1="16" x2="12.01" y2="16"></line>
                                        </svg>
                                        <span class="text-sm font-medium">{{ $message }}</span>
                                    </div>
                                </div>
                            @enderror
                            @error('files.*')
                                <div class="mt-3 p-3 rounded-lg bg-red-50 border border-red-200">
                                    <div class="flex items-center gap-2 text-red-600">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16"
                                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                            stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5">
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <line x1="12" y1="8" x2="12" y2="12"></line>
                                            <line x1="12" y1="16" x2="12.01" y2="16"></line>
                                        </svg>
                                        <span class="text-sm font-medium">{{ $message }}</span>
                                    </div>
                                </div>
                            @enderror
                                        </div>
                                    </div>
                                </div>




                </div>

                <div class="space-y-6 md:col-span-2">
                    <div class="rounded-xl border bg-white shadow-lg">
                        <div class="border-b border-gray-100 p-6">
                            <h3 class="text-lg font-semibold text-gray-900">Pricing</h3>
                            <p class="text-sm text-gray-600">Set your product pricing</p>
                        </div>
                        <div class="p-6 space-y-5">
                            <div class="space-y-2">
                                <label for="price" class="block text-sm font-medium text-gray-700">
                                    Price (Rp) <span class="text-red-500">*</span>
                                </label>
                                <input type="number" name="price" id="price" step="0.01" min="5000"
                                    class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                    placeholder="29999" value="{{ old('price', $product->price) }}" required>
                                <p class="mt-1 text-xs text-gray-500">Minimum price is Rp 5,000</p>
                                @error('price')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            <div class="flex items-center space-x-3">
                                <input type="checkbox" name="has_discount" id="has_discount"
                                    class="rounded border-gray-200 text-indigo-600 focus:ring-indigo-500">
                                <label for="has_discount" class="text-sm font-medium text-gray-700">Enable discount
                                    price</label>
                            </div>
                            <div id="discount-price-container"
                                class="space-y-2 {{ (old('has_discount') || $product->discount_price) ? '' : 'hidden' }}">
                                <label for="discount_price" class="block text-sm font-medium text-gray-700">Discount Price
                                    (Rp)</label>
                                <input type="number" name="discount_price" id="discount_price" step="0.01"
                                    min="5000"
                                    class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors"
                                    placeholder="19999" value="{{ old('discount_price', $product->discount_price) }}">
                                <p class="mt-1 text-xs text-gray-500">Minimum discount price is Rp 5,000</p>
                                @error('discount_price')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Chatbot Configuration -->
                    <div class="rounded-xl border bg-white shadow-lg">
                        <div class="border-b border-gray-100 p-6">
                            <h3 class="text-lg font-semibold text-gray-900">AI Chatbot</h3>
                            <p class="text-sm text-gray-600">Configure AI assistant for this product</p>
                        </div>
                        <div class="p-6">
                            @php
                                $chatbotData = $product->chatbotData;
                                $canActivateChatbot = Auth::user()->canActivateChatbot();
                                $currentTier = Auth::user()->getCurrentMembershipTier();
                            @endphp

                            <div class="space-y-4">
                                @if($currentTier && ($currentTier->chatbot_products_limit > 0 || $currentTier->chatbot_products_limit === -1))
                                    <!-- Membership Status Display -->
                                    <div class="bg-gradient-to-r {{ $currentTier->slug === 'basic' ? 'from-gray-50 to-gray-100 border-gray-300' : 'from-yellow-50 to-yellow-100 border-yellow-300' }} border-2 rounded-lg p-4">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                @if($currentTier->slug === 'basic')
                                                    <!-- Silver Badge -->
                                                    <div class="flex items-center px-3 py-1 rounded-full bg-gradient-to-r from-gray-400 to-gray-600 text-white text-xs font-bold mr-3">
                                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                                        </svg>
                                                        SILVER
                                                    </div>
                                                @else
                                                    <!-- Gold Badge -->
                                                    <div class="flex items-center px-3 py-1 rounded-full bg-gradient-to-r from-yellow-400 to-yellow-600 text-yellow-900 text-xs font-bold mr-3">
                                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                                        </svg>
                                                        GOLD
                                                    </div>
                                                @endif
                                                <div>
                                                    <p class="text-sm font-semibold text-gray-900">{{ $currentTier->name }} Membership Active</p>
                                                    <p class="text-xs text-gray-600">
                                                        Chatbot Limit: {{ $currentTier->chatbot_products_limit === -1 ? 'Unlimited' : $currentTier->chatbot_products_limit }} products
                                                    </p>
                                                </div>
                                            </div>
                                            <div class="text-right">
                                                <svg class="w-8 h-8 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Chatbot Status and Configuration -->
                                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                        <div class="flex items-center justify-between mb-3">
                                            <div class="flex items-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                                </svg>
                                                <div>
                                                    <p class="text-sm font-medium text-blue-900">Chatbot Status</p>
                                                    <p class="text-xs text-blue-700">
                                                        @if($chatbotData && $chatbotData->is_active)
                                                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                                                </svg>
                                                                Active & Ready
                                                            </span>
                                                        @elseif($chatbotData)
                                                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                                                                </svg>
                                                                Configured but Inactive
                                                            </span>
                                                        @else
                                                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                                                </svg>
                                                                Not Configured
                                                            </span>
                                                        @endif
                                                    </p>
                                                </div>
                                            </div>
                                            @if($chatbotData)
                                            <div class="text-right">
                                                <p class="text-xs text-gray-500">Completion</p>
                                                <div class="flex items-center">
                                                    <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                                        <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $chatbotData->completion_percentage }}%"></div>
                                                    </div>
                                                    <p class="text-sm font-semibold text-blue-600">{{ $chatbotData->completion_percentage }}%</p>
                                                </div>
                                            </div>
                                            @endif
                                        </div>

                                        <a href="{{ route('seller.products.chatbot', $product) }}"
                                           class="inline-flex items-center justify-center w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
                                                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                                            </svg>
                                            {{ $chatbotData ? 'Configure Chatbot' : 'Setup Chatbot' }}
                                        </a>
                                    </div>

                                    @if(!$canActivateChatbot && !($chatbotData && $chatbotData->is_active))
                                    <div class="bg-amber-50 border border-amber-200 rounded-lg p-4">
                                        <div class="flex items-center">
                                            <svg class="w-5 h-5 text-amber-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                                            </svg>
                                            <div>
                                                <p class="text-sm font-medium text-amber-800">Chatbot Limit Reached</p>
                                                <p class="text-xs text-amber-700">
                                                    You've used {{ $currentTier->chatbot_products_limit }} of {{ $currentTier->chatbot_products_limit }} available chatbots.
                                                    <a href="{{ route('membership.index') }}" class="underline hover:text-amber-900 font-medium">Upgrade to Pro</a>
                                                    for unlimited chatbots.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    @endif
                                @else
                                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 text-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mx-auto mb-4 text-gray-400">
                                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                                        </svg>
                                        <h4 class="text-lg font-semibold text-gray-900 mb-2">AI Chatbot Not Available</h4>
                                        <p class="text-sm text-gray-600 mb-4">
                                            Upgrade your membership to unlock AI-powered chatbots for your products
                                        </p>
                                        <div class="bg-white rounded-lg p-4 mb-4 border border-gray-200">
                                            <p class="text-xs text-gray-500 mb-2">Current tier: <span class="font-medium">{{ $currentTier ? $currentTier->name : 'Starter (Free)' }}</span></p>
                                            <p class="text-xs text-gray-500">Chatbot access: <span class="text-red-600 font-medium">Not included</span></p>
                                        </div>
                                        <a href="{{ route('membership.index') }}"
                                           class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
                                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                            </svg>
                                            Upgrade Membership
                                        </a>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="rounded-xl border bg-white shadow-lg">
                        <div class="border-b border-gray-100 p-6">
                            <h3 class="text-lg font-semibold text-gray-900">Publishing</h3>
                            <p class="text-sm text-gray-600">Control your product visibility</p>
                        </div>
                        <div class="p-6 space-y-5">
                            <div x-data="{ status: '{{ old('status', $product->status) }}' }">
                                <div class="space-y-2">
                                    <label class="block text-sm font-medium text-gray-700">Status</label>
                                    <select name="status" x-model="status"
                                        class="block w-full rounded-lg border-gray-200 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors">
                                        <option value="draft">Draft</option>
                                        <option value="active">Published</option>
                                    </select>
                                </div>
                                <div x-show="status === 'draft'" class="mt-4">
                                    <p class="text-sm text-gray-500">
                                        Save as draft to continue working on it later. It won't be visible to customers.
                                    </p>
                                </div>
                                <div x-show="status === 'active'" class="mt-4">
                                    <p class="text-sm text-gray-500">
                                        Publish your product to make it available for purchase immediately.
                                    </p>
                                </div>
                                <div class="flex flex-col gap-3 pt-4">
                                    <button type="submit"
                                        :class="{
                                            'bg-amber-600 hover:bg-amber-700': status === 'draft',
                                            'bg-emerald-600 hover:bg-emerald-700': status === 'active'
                                        }"
                                        class="inline-flex w-full items-center justify-center rounded-lg px-4 py-2 text-sm font-medium text-white shadow-md transition-colors">
                                        <span
                                            x-text="status === 'active' ? 'Update and Publish' : 'Update as Draft'"></span>
                                    </button>
                                    <a href="{{ route('seller.products.index', [], false) ?? '#' }}"
                                        class="inline-flex w-full items-center justify-center rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 transition-colors">
                                        Cancel
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    @push('scripts')
        {{-- load tinymce --}}
        <script src="{{ asset('tinymce/tinymce.min.js') }}"></script>
    @endpush
@endsection
