<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto space-y-8">
        <h1 class="text-2xl font-bold">Upload Functionality Test</h1>
        
        <!-- Multiple Images Upload Test -->
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-lg font-semibold mb-4">Multiple Images Upload</h2>
            <div class="rounded-lg border-2 border-dashed border-gray-200 p-8 upload-area">
                <div class="flex flex-col items-center justify-center gap-3 text-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                        stroke-linecap="round" stroke-linejoin="round" class="h-10 w-10 text-gray-400">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="17 8 12 3 7 8"></polyline>
                        <line x1="12" y1="3" x2="12" y2="15"></line>
                    </svg>
                    <p class="text-sm font-medium text-gray-700">Drag and drop images here or click to browse</p>
                    <p class="text-xs text-gray-500">Upload product images (JPEG, PNG, JPG, GIF, max 2MB each, up to 10 images)</p>
                    <input id="images" name="images[]" type="file" class="hidden" accept="image/jpeg,image/png,image/jpg,image/gif" multiple>
                    <button type="button" class="choose-images mt-2 inline-flex items-center rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 transition-colors">
                        Choose Images
                    </button>
                </div>
            </div>
            <div id="images-preview" class="space-y-2 hidden">
                <label class="block text-sm font-medium text-gray-700">Uploaded Images</label>
                <div id="images-list" class="space-y-2"></div>
            </div>
        </div>

        <!-- Single Image Upload Test -->
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-lg font-semibold mb-4">Single Image Upload</h2>
            <div class="rounded-lg border-2 border-dashed border-gray-200 p-8 upload-area">
                <div class="flex flex-col items-center justify-center gap-3 text-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                        stroke-linecap="round" stroke-linejoin="round" class="h-10 w-10 text-gray-400">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="17 8 12 3 7 8"></polyline>
                        <line x1="12" y1="3" x2="12" y2="15"></line>
                    </svg>
                    <p class="text-sm font-medium text-gray-700">Drag and drop image here or click to browse</p>
                    <p class="text-xs text-gray-500">Upload single image (JPEG, PNG, JPG, GIF, max 2MB)</p>
                    <input id="image" name="image" type="file" class="hidden" accept="image/jpeg,image/png,image/jpg,image/gif">
                    <button type="button" class="choose-image mt-2 inline-flex items-center rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 transition-colors">
                        Choose Image
                    </button>
                </div>
            </div>
            <div id="image-preview" class="space-y-2 hidden">
                <label class="block text-sm font-medium text-gray-700">Uploaded Image</label>
                <div id="image-list" class="space-y-2"></div>
            </div>
        </div>

        <!-- Files Upload Test -->
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-lg font-semibold mb-4">Files Upload</h2>
            <div class="rounded-lg border-2 border-dashed border-gray-200 p-8 upload-area">
                <div class="flex flex-col items-center justify-center gap-3 text-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                        stroke-linecap="round" stroke-linejoin="round" class="h-10 w-10 text-gray-400">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="17 8 12 3 7 8"></polyline>
                        <line x1="12" y1="3" x2="12" y2="15"></line>
                    </svg>
                    <p class="text-sm font-medium text-gray-700">Drag and drop files here or click to browse</p>
                    <p class="text-xs text-gray-500">Upload files (ZIP, PDF, DOCX, XLSX, max 20MB each, up to 5 files)</p>
                    <input id="files" name="files[]" type="file" class="hidden" multiple accept=".zip,.pdf,.epub,.docx,.doc,.xlsx,.pptx,.txt,.mobi,.azw,.azw3">
                    <button type="button" class="choose-files mt-2 inline-flex items-center rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 transition-colors">
                        Choose Files
                    </button>
                </div>
            </div>
            <div id="file-preview" class="space-y-2 hidden">
                <label class="block text-sm font-medium text-gray-700">Uploaded Files</label>
                <div id="file-list" class="space-y-2"></div>
            </div>
        </div>
    </div>

    <script>
        // Simple test script to verify button clicks work
        document.addEventListener('click', function(event) {
            console.log('Click detected on:', event.target);
            
            // Handle choose-images buttons
            if (event.target.matches('.choose-images') || event.target.closest('.choose-images')) {
                console.log('Choose images button clicked!');
                event.preventDefault();
                event.stopPropagation();
                const imagesInput = document.getElementById("images");
                if (imagesInput) {
                    console.log('Triggering images input click');
                    imagesInput.click();
                } else {
                    console.error('Images input not found!');
                }
                return;
            }
            
            // Handle choose-image buttons
            if (event.target.matches('.choose-image') || event.target.closest('.choose-image')) {
                console.log('Choose image button clicked!');
                event.preventDefault();
                event.stopPropagation();
                const imageInput = document.getElementById("image");
                if (imageInput) {
                    console.log('Triggering image input click');
                    imageInput.click();
                } else {
                    console.error('Image input not found!');
                }
                return;
            }
            
            // Handle choose-files buttons
            if (event.target.matches('.choose-files') || event.target.closest('.choose-files')) {
                console.log('Choose files button clicked!');
                event.preventDefault();
                event.stopPropagation();
                const fileInput = document.getElementById("files");
                if (fileInput) {
                    console.log('Triggering files input click');
                    fileInput.click();
                } else {
                    console.error('Files input not found!');
                }
                return;
            }
        });

        // Test file input change events
        document.getElementById('images').addEventListener('change', function() {
            console.log('Images selected:', this.files.length);
        });

        document.getElementById('image').addEventListener('change', function() {
            console.log('Image selected:', this.files.length);
        });

        document.getElementById('files').addEventListener('change', function() {
            console.log('Files selected:', this.files.length);
        });
    </script>
</body>
</html>
